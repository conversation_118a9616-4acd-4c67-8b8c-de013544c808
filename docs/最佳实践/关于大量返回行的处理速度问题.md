### 背景

当查询的结果行达到3万行+，每行的列字段超过30个时，查询结果的解析环节会超过2秒，即便数据库的查询和网络传输只有几十毫秒。也就是说，时间消耗在数据到达Java应用之后的解析上。

这个性能目前主要消耗在jdbc和jdbcTemplate上，对于3万行的数据，解析时间如下：

0、使用mysql客户端如sqlyog等，耗时100毫秒
1、纯jdbc ResultSet遍历获取，耗时4500毫秒
2、jdcbTemplate查询回List<Map<String, Object>>的列表，耗时5400毫秒
3、Nimble-ORM查询回List<XxxDO>的列表，耗时5700毫秒

因此，要进行优化，得从jdbc入手，经过试验，发现调整fetchSize等方式没有什么效果。

后面想到一种并发查询的方式。

### 解决方案

假设要查询的SQL是：

```sql
select * from t_demo
```

数据量有几万条，那么可以通过id取模的方式，将sql拆分为10条并发的SQL给数据库执行，使得java这边可以使用10条线程并发解析数据：

```sql
select * from t_demo where id % 10 = 0 -- 取模后的值从0到9
```

Java这一侧这用线程池的方式，并发查询，最后再组装起来。