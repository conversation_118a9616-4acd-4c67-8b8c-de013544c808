2025年7月5日
v1.7.6 - [add] DBHelper接口增加boolean getFeatureStatus(FeatureEnum featureEnum)接口查询当前特性开关状态
       - [enhance] 优化AnnotationSupportRowMapper性能：当@Column注解的列在ResultSet中找不到时的性能

2025年5月13日
v1.7.5 - [enhance] 优化批量delete时，DO类带有deleteValueScript的性能，支持批量性能了
       - [enhance] 优化AnnotationSupportRowMapper，提升5%性能

2025年4月23日
v1.7.4 - [enhance] 对于getRaw方法，自动移除sql末尾的分号
       - [enhance] 优化delete(Class,postSql,args)方法，支持有拦截器且DO类没有key的情况

2025年1月23日
v1.7.3 - [add] 增加DBContext.getRunningSql方法，查询当前正在执行的SQL

2025年1月16日
v1.7.2 - [add] 支持打印执行中尚未结束的慢SQL，默认执行超过60秒未返回，打印慢sql
       - [del] 移除isExistAtLeast和getAllKey方法

2024年12月25日
v1.7.1 - [add] DBHelper.getRaw方法增加支持内置类型RowData，该类型获取指定数据类型要优于Map.class
       - [enhance] 增加检查：不允许DO字段使用枚举类型，不允许SQL入参中存在枚举类型参数，否则抛出EnumNotSupportedException
       - [enhance] 当用户使用java.time.YearMonth类型作为入参时，自动处理成String类型
       - [fix] 修复当DBHelper被SpringAOP代理时，@RelateColumn功能指定dbHelper beanName时，抛出NullPointerException的问题

2024年11月23日
v1.7.0 - [refactor]【升级须知】IDBHelperDataService名称变更为DBHelperDataService; IDBHelperSlowSqlCallback变更为DBHelperSlowSqlCallback
                    DBHelperSlowSqlCallback的callback参数增加int batchSize参数，可以获得批量操作的数据量
       - [add] 增加DBHelperSqlCallback，可以获得执行的所有sql
       - [add] 增加@WhereColumn注解，调用WhereSQL.buildFromAnnotation可以生成WhereSQL
       - [add] DBHelper对于所有接受String postSql, Object... param参数的方法，都增加支持WhereSQL传参
       - [add] 支持创建DBHelper对象时，由用户指定数据库类型，这样可以节省启动时连接数据库的时间
       - [deprecated] 标识废弃isExistAtLeast和getAllKey方法
       - [enhance] lazy确定数据库类型默认值由false改为true，不会因为数据库原因导致应用起不来
       - [add] WhereSQL和WhereSQLForNamedParam增加page()方法

2024年10月25日
v1.6.8 - [enhance] 支持自动降级处理mysql max package太小的限制
       - [fix] 修复当使用isJSON=true的列，其DTO中有LocalDateTime类型，带有不为3位的纳秒数时解析错误的问题
       - [enhance] 重写了LocalDateTime和LocalDate、LocalTime的解析器，覆盖ISO 8601所有格式

2024年8月28日
v1.6.7 - [fix] 修复del表方式的软删除，删除数据丢失的问题
       - [enhance] 当请求参数有多个，但入参仅有一个list时，提示用户转成数组

2024年7月15日
v1.6.6 - [downgrade] 由于jsqlparser 5.0只支持java 11以上，因此先降级jsqlparser为4.9，最后一个支持jdk8的版本，1年后orm将升级到java11以上

2024年7月11日
v1.6.5 - [enhance] 优化select SQL，减少不必要的AS写法
       - [fix] 修复getRaw方法当参数是Map.class形式，值为空List和空Set时，没有自动处理的问题
       - [add] 支持设置延迟探测数据库类型，默认关闭

2024年5月23日
v1.6.4 - [add] 增加@SqlColumn注解，用以获得DO对应的实际执行的sql
       - [add] SQLAssemblyUtils增加拼凑WhereSQL为实际可执行sql的工具方法

2024年5月2日
v1.6.3 - [add] 打印SQL时增加打印该SQL对应的源码文件和行号
       - [enhance] logSQL时直接将sql和参数合并成可执行的SQL打印出来

2024年3月26日
v1.6.2 - [enhance] 当getRaw/getRawOne/getRawForStream的args参数只传入Map类型时，自动转成单Map参数的方法
       - [add] 增加将SQL和参数整合成可执行SQL的工具类SQLAssemblyUtils

2024年2月5日
v1.6.1 - [del] 移除@Table的sameTableNameAs属性，原因：表名非常重要，经常通过搜索来确定表有没有DO类，而用sameTableNameAs则较难找到
       - [add] 支持@Table写其它数据库的名称，查到到其它数据库的表
       - [modify] 【升级须知】DBHelper的setTimeoutWarningValve更名为setSlowSqlWarningValve，setTimeoutWarningCallback更名为setSlowSqlWarningCallback，功能不变

2023年11月28日
v1.6.0 - [add] 增加PostgreSQL的支持
       - [enhance] 【升级须知】对于clickhouse，byte[]数据类型自动转换成base64存储，查询时自动还原；
                              对于历史的使用了byte[]类型存储到clickhouse的数据，需要手工转换成base64以保证查回正确
       - [enhance] 【升级须知】拦截器基类DBHelperInterceptor改成接口
       - [add] 增加JoinTable的属性JoinTypeAsString，支持自定义写join的类型

2023年10月28日
v1.5.7 - [fix] 修复getDatabaseType方法没有关闭Connection的问题，它会占用一个数据库连接，修复后不占用
       - [enhance] 对于virtualTable的postSql，自动在前面加上回车符，避免自定义sql最后一行是注释且没有换行时，postSql失效
       - [add] 新增getDatabaseType()方法，支持获得数据库类型
       - [compatible] 兼容Clickhouse不支持force index

2023年8月27日
v1.5.6 - [fix] 修复当Table为virtualTable虚拟表时，where条件没有写时，生成的SQL语法错误的问题

2023年8月23日
v1.5.5 - [enhance] 分页limit的偏移修改成offset，更标准，支持更多的数据库
       - [fix] 修复当SQL包含注释且注释中出现问号?的情况下，处理SQL参数出错的问题
       - [add] 支持配置增删时，对DO的字符串字段进行trim操作

2023年6月26日
v1.5.4 - [add] 增加int insertBatchWithoutReturnId(String tableName, Collection<Map<String, Object>> list)和
                   int insertBatchWithoutReturnId(String tableName, List<String> cols, Collection<Object[]> values) 方法
                   支持指定表名和动态的数据进行批量插入
       - [add] 支持设置启用默认值的条件，默认是null值时启用，还支持String为empty和blank时启用

2023年5月27日
v1.5.3 - [add] 支持在DO类的级别，按类的类型设置列的默认值

2023年5月16日
v1.5.2 - [add] JoinLeftTable/JoinRightTable支持force index
       - [upgrade] jsqlparser由4.5升级到4.6; slf4j-api由1.7.36升级到2.0.7;jackson升级到2.15.0;mvel2升级到2.4.15.Final

2023年4月30日
v1.5.1 - [add] WhereSQL的limit支持long类型，支持更大的offset数值
       - [del] 移除setTableName和resetTableNames方法，推荐使用withTableNames方法

2023年4月14日
v1.5.0 - [del] 移除deleteByKey 3个方法，请用delete方法代替
       - [del] 移除turnOffSoftDelete和turnOnSoftDelete方法
       - [add] 提供withTableNames方法，支持动态自定义表名
       - [add] 提供@Table的softDeleteTable和softDeleteDBHelperBean属性，支持第二种方式的软删除
       - [add] WhereSQL增加andIf/orIf/notIf方法，支持条件判断
       - [add] 新增开启和关闭缓存的开关，默认开启缓存，支持关闭缓存
       - [add] 增加@Table和@RelatedColumn的缓存
       - [add] 对于MySQL慢sql，自动进行explain分析，打印分析结果，默认开启，支持关闭

2023年4月12日
v1.4.6 - [deprecated] 废弃deleteByKey(Class<T> clazz, Object keyValue)方法
       - [deprecated] deleteByKey(T t)和deleteByKey(Collection<T> list)标记重命名为delete(T t)和deleteByKey(Collection<T> list)
       - [deprecated] 标记废弃turnOffSoftDelete和turnOnSoftDelete方法
       - [add] 增加deleteHard(Collection<T> list)、deleteHard(Class<T> clazz, String postSql, Object... args)方法
       - [enhance] 优化insertOrUpdate方法，支持批量，提高性能

2023年4月8日
v1.4.5 - [add] 支持真正的批量update，提高批量update的性能10-100倍
       - [enhance] 数据库类型databaseType的获取支持lazy初始化，不会因为数据库连接异常导致Spring容器启动不了
       - [add] WhereSQL新增getSQLForWhereAppend方法，用于追加到where and的SQL片段
       - [add] 提供WhereSQLForNamedParam，支持命名参数的whereSQL

2023年3月2日
v1.4.4 - [enhance] clickhouse22.2以下版本不支持insert into default，因此改成null，支持全版本clickhouse
       - [enhance] clickhouse的批量插入使用jdbcTemplate.batchUpdate的方式进行，insert多values性能不佳

2023年2月28日
v1.4.3 - [enhance] 优化接口定义，setTableName/resetTableNames/turnOffSoftDelete/turnOnSoftDelete
                   /setGlobalComment/setLocalComment定义为接口静态方法，对所有DBHelper实例生效
                   【升级须知】对于这6个方法的调用，需要由实例方式调用改成接口静态方法调用
       - [add] 增加nimbleorm内置JSON解析对LocalDate/LocalDateTime/LocalTime的扩展支持，支持更多的时间日期格式
       - [enhance] 兼容字符串类型的字段误注解了isAutoIncrement=true的情况，可以正常处理
       - [del] 删除@ExcludeInheritedColumn，推荐使用@Table的sameTableNameAs指定表名或直接指定表名
       - [del] 删除SubQuery子查询参数功能
       - [del] 删除insertOrUpdateFull、insertOrUpdateFullWithNull方法
       - [del] 删除insertOrUpdateWithNull(list)、updateWithNull(list)方法
       - [del] 删除boolean getByKey(T t)方法
       - [del] 删除<T, K> Map<K, T> getByKeyList(Class<T> clazz, Collection<K> keyValues)方法

2023年1月7日
v1.4.2 - [add] 批量插入支持数据库默认值(即当插入为null时使用数据库默认值)，此外mysql不需要再设置rewriteBatchedStatements=TRUE参数
       - [enhance] 优化批量插入日志和不插入不需要的字段，提升50+%批量插入性能
       - [enhance] 优化insert(list)方法，当满足批量插入条件(主键有值)时，自动转换成批量插入
       - [enhance] @Column的setTimeWhenInsert/Update/Delete的属性支持除了Date外的LocalDateTime/LocalDate等7种类型
       - [deprecated] 标记废弃insertOrUpdateFull、insertOrUpdateFullWithNull方法
       - [deprecated] 标记废弃insertOrUpdateWithNull(list)、updateWithNull(list)方法

2022年12月24日
v1.4.1 - [add] 支持JoinTable中写@RelatedColumn直接关联DO/JoinTable类
       - [add] 增加@Table的virtualTableSQL/virtualTablePath属性，支持手工写表SQL并表现为一个DO类
       - [add] 增加@Table的sameTableNameAs指定表名
       - [deprecated] 标记废除@ExcludeInheritedColumn，推荐使用@Table的sameTableNameAs指定表名或直接指定表名
       - [deprecated] 标记废除SubQuery
       - [del] 删除getRawCount方法

2022年12月14日
v1.4.0 - [enhance] 将单元测试代码独立出来
       - [enhance] 优化批量插入的SQL log

2022年10月28日
v1.3.7 - [add] 支持Stream流式查询方法：getAllForStream和getRawForStream方法
       - [deprecated] 标记废弃getRawCount方法

2022年10月21日
v1.3.6 - [enhance] 对空List的SQL参数进行自动处理时，不改变原List的值
       - [enhance] 对于字段是java.util.Date类型，明确转换为java.util.Date类型，不再使用其子类例如java.sql.Date
       - [enhance] 对于where in的空list查询，自动补充null，代替魔数，此方案对clickhouse的类型兼容性更好

2022年9月19日
v1.3.5 - [fix] 修复getRaw传递Map.class时，无法拿到字段as别名的bug
       - [enhance] 对不支持rs.getTimestamp的驱动，增加orm解析Date的支持
       - [enhance] 支持全为0000-00-00 00:00:00的日期的解析，低版本java可能解析失败

2022年8月29日
v1.3.4 - [add] DBHelper增加getRawOne方法，支持只读取第一行
       - [add] @Column增加readIfNullScript脚本，支持设置指定的默认值
       - [add] JSON相关功能支持LocalDate类型解析

2022年8月3日
v1.3.3 - [enhance] 当RelatedColumn的localColumn和remoteColumn属性值有错时抛出异常，代替原来的error日志
       - [enhance] RelatedColumn的localColumn和remoteColumn属性不区分大小写

2022年8月2日
v1.3.2 - [add] @RelatedColumn支持指定DBHelper的bean名称来指定不同的数据源
       - [add] @Column @RelatedColumn @Table @JoinTable 增加comment注释属性
       - [upgrade] 升级jsqlparser到4.5

2022年7月11日
v1.3.1 - [enhance] 移除当@RelatedColumn使用conditional属性时，关联不上对象时打印的error日志(不影响结果正确性)

2022年7月8日
v1.3.0 - [add] 增加executeRaw方法，支持执行自定义的SQL
       - [add] 支持给执行的SQL加注释，全局注释和线程范围注释，便于全链路跟踪
       - [add] @RelatedColumn增加conditional属性，支持指定条件下不处理该@RelatedColumn
       - [enhance] 对分页查询getPage，如果用户没有指定order by，自动加上order by
       - [add] 增加WhereSql构造器，提高构造where查询子句的效率
       - [del] 移除Column的insertDefault属性，该属性以后由insertValueScript代替
       - [enhance] 对于delete(Class, String, Object...)方法，使用了deleteValueScript的DO类将逐个删除，会降低批量删除性能
       - [enhance] 整理和抽取代码,提高代码测试覆盖率至91%

2022年6月17日
v1.2.3 - [fix] 修复当条件为where in (?)且参数为空list，且查询列为数字类型时，查出数据的bug

2022年5月12日
v1.2.2 - [del] 移除标记为废弃的insertWhereNotExist和insertWithNullWhereNotExist方法
       - [deprecated] 标记废弃Column的insertDefault属性，该属性以后由insertValueScript代替
       - [enhance] 增加获取Column的缓存以提高性能

2022年5月7日
v1.2.1 - [add] 添加接收JdbcTemplate的DBHelper构造函数
       - [enhance] 自动移除getOne和getPage时，postSql参数中的limit子句

2022年4月24日
v1.2.0 - [del] 去掉namedParameterJdbcTemplate的设置，从1.2起不需要再设置namedParameterJdbcTemplate了
       - [modify] insertValueScript仅在值为null时才进行设置

2022年2月14日
v1.1.5 - [add] 当DO类注解的列在返回结果中不存在时，由直接报错改成不处理，这种情况只会在getRaw时出现，由用户自行测试处理；
               也支持通过配置项THROW_EXCEPTION_IF_COLUMN_NOT_EXIST设置为直接报错
       - [add] getRaw支持传入Map.class，传回Map<String, Object>类型的list

2021年12月15日
v1.1.4 - [add] 支持@RelatedColumn中写limit子句，明确说明这样走不了批量，性能会显著下降
       - [enhance] 当@RelatedColumn的extraWhere SQL语法错误时，抛出异常，原来是忽略extraWhere，可能导致错误

2021年12月8日
v1.1.3 - [add] 支持手工开启log.info打印实际执行的sql，默认是以log.debug级别打印；
               使用turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL)开启

2021年11月18日
v1.1.2 - [enhance] JSON=true的成员变量支持嵌套泛型和大于2个的泛型，之前只支持1或2个泛型

2021年10月18日
v1.1.1 - [enhance] 对于select的字段，如果已经出现重复的名称，那么子类会覆盖父类；DO的setter同理
                   【注意】升级至此版本，请先检查下项目一个DO类及其子类@Column注解有没有出现value重复的情况，确保没有重复
       - [enhance] getByKeyList/insert/update/delete等的接口参数类型由List扩展为Collection

2021年9月23日
v1.1.0 - [add] 【重要升级和变动】RelatedColumn支持多列进行关联查询；
                IDBHelperDataService接口定义发生变化，客户端需要变化。

2021年9月23日
v1.0.5 - [add] getRaw方法支持转换基本类型，如String.class/Integer.class
       - [add] PageData类增加trans方法，支持转变类型

2021年9月20日
v1.0.4 - [add] 增加当计算列是sum()时，自动将null转成0的特性，并支持关闭该特性
       - [fix] 修复当计算列参与到postSql计算时，getCount计算总数时抛出SQL语法错误的bug

2021年8月14日
v1.0.3 - [add] 增加getByExample方法
       - [enhance] turnOffSoftDelete和turnOnSoftDelete支持传入多个参数
       - [enhance] 增加3种日期格式的解析

2021年6月30日
v1.0.2 - [add] 新增getRaw支持使用namedParameter的方式查询

2021年6月10日
v1.0.1 - [del] 移除类缓存的WARN日志，框架允许没有setter方法
       - [fix] 对于字段是java.util.Date类型，只查询到日期而查询不到时间的问题
       - [enhance] 对于需要传参的调用，如果SQL占位符的个数和给定的参数不一致，则打ERROR日志(不会停止查询)；这是对jdbcTemplate的优化，jdbcTemplate只会检查出参数个数小于占位符个数的情况，不会检查出参数个数大于占位符个数，但这往往是错误的

2021年2月18日
v1.0.0 - [upgrade] 【重要】jdk最低支持版本由1.6升级为1.8，不再支持低版本jdk 1.6
       - [upgrade] 升级jsqlparser2.1->4.0
       - [del] 【升级须知】去掉updateAll接口的拦截器afterUpdate方法的调用，对于此接口的拦截器依赖，请考虑使用canal方法代替
       - [add] 关联查询RelatedColumn支持对computed计算值进行关联，从而支持多字段关联查询
       - [enhance] getCount和Page的total等返回记录总数的类型由int升级为long
       - [enhance] IDBHelperDataService接口的返回值泛型化，支持各种类型返回，实现者不需要强制转换成Object了
       - [fix] 修复当RelateColumn注解的remoteColumn的值是SQL关键词时导致SQL异常的问题
       - [enhance] 使用缓存的方式优化AnnotationSupportRowMapper的性能，4000行处理从1830毫秒减少230毫秒为1600毫秒
       - [enhance] 对于字段是java.util.Date类型，明确转换为java.util.Date类型，不再使用其子类例如java.sql.Date
       - [add] 增加对类型LocalDateTime LocalDate LocalTime的支持
       - [fix] 当RelateColumn是oneToOne时，取匹配中的第一个，原来是取匹配中的最后一个，这个对于排序时的映射有影响
       - [add] 增加批量插入方法insertBatchWithoutReturnId，该方法不会返回自增id
       - [add] 增加软删除时，支持设置软删除列为id的值，这样便于软删除的表，建立有业务规则的唯一索引

2020年12月16日
v0.9.9 - [add] 增加getRaw和getRawCount方法，支持自定义SQL查询，适用于统计类SQL
       - [add] 增加join类型STRAIGHT_JOIN

2020年6月20日
v0.9.8 - [improve] 使用jdbc keyHolder替换SELECT LAST_INSERT_ID()，减少对MySQL及事务的依赖
       - [add] 增加线程范围内(临时)关闭指定类的软删除设置，turnOffSoftDelete
       - [del] 标记废弃insertWhereNotExist和insertWithNullWhereNotExist两个方法，短期内不会删除，但标记为在高并发情况下，可能有性能问题，如果使用，请加好索引
       - [upgrade] 升级jackson至2.10.4，mvel至2.4.8.Final
       - [test] 完善单元测试，IDEA coverage代码覆盖率从88%提升至90%

2020年6月4日
v0.9.7 - [add] Column支持对String类型的字段设置最大字符数
       - [del] 重大变化：去掉了插入和更新的@Transactional注解，以后多条插入或更新时，是否需要事务由调用者自行决定，因此此版本开始，能支持多数据源多事务

2019年12月20日
v0.9.6 - [improve] 当分页查询传入page小于1时，抛出InvalidParameterException异常
       - [rename] 重名JSON为NimbleOrmJSON，重命名DateUtils为NimbleOrmDateUtils，避免被外部误引用到
       - [modify] 基于SQL92定义，使用count(*)代替原来的count(1)，两者功能是等价的
       - [add] NimbleOrmDateUtils增加支持时间戳的日期解析

2019年9月6日
v0.9.5 - [improve] 替换SQL_CALC_FOUND_ROWS以提升性能，接口、功能逻辑及正确性无变化

2019年9月3日
v0.9.4 - [add] 支持动态指定表名，适合于分表的场景
       - [update] jsqlparser升级至2.1
       - [improve] 明确@Table的alias别名的影响范围
       - [move] 将SubQuery从bean包移动到model包
       - [fix] 增加java.util.Date到java.sql.Time的类型映射

2019年5月22日
v0.9.3 - [add] 增加新增、修改、删除操作时，自动通过mvel脚本获取值的能力

2019年5月9日
v0.9.2 - [update] 升级jsqlparser为2.0，升级jackson为2.9.8
       - [del] 移除nimble-orm对双引号表示字符串的支持，只支持单引号表示
       - [improve] 完善文档说明，简化代码；修改updateCustom方式不支持casVersion的笔误，实际上支持

2019年4月1日
v0.9.1 - [add] 增加乐观锁支持，在Column上指定casVersion为true即生效
       - [rename] DBHelperInterceptor拦截器中方法beforeUpdateCustom修改为beforeUpdateAll

2018年9月8日
v0.9.0 - 【升级须知】setTimeWhenUpdate在软删除时不会再修改时间，请加上setTimeWhenDelete=true
         [add] @Column增加setTimeWhenDelete属性，delete操作时自动设置时间
         [del] @Column属性setTimeWhenUpdate，在软删除时不会自动设置当前时间
         [improve] 自动设置的时间精确到毫秒

2018年7月19日
v0.8.7 - [improve] 优化@RelatedColumn性能，满足万级数据关联性能

2018年7月12日
v0.8.6 - [add] 增加maxPageSize设置，防止pageSize忘记限制而过大导致的数据库压力过大问题

2018年6月11日
v0.8.5 - [fix] 修复insertOrUpdateFull方法当dbList参数值为null抛出空指针异常的问题

2018年6月4日
v0.8.4 - [improve] JSON转换支持将空字符设置为null赋值给对象

2018年5月25日
v0.8.3 - [fix] 修复拦截器偶发可能传错参数的问题，该问题从0.5.0存在至今

2018年5月23日
v0.8.2 - [improve] 对于not in(?)表达式，如果传入的参数为空，那么等价于所有值被匹配到。原来是不匹配。
       - [add] 增加SubQuery参数，支持传入in (?)表达式，彻底处理了软删除标记位在代码中出现的情况
       - [improve] 拦截器中Object[]的参数改为List<Object>类型，方便拦截方处理

2018年5月13日
v0.8.1 - [improve] 优化updateAll方法的性能

2018年5月11日
v0.8.0 - [improve] 完美解决updateAll和delete拦截器after无法获得实际修改的记录的问题
       - [del] 删除拦截器中afterDeleteCustom、beforeDeleteCustom和afterUpdateCustom共3个方法
       - [del] 删除insertWithNullInOneSQL方法，该方法获得性能的同时却无法获得插入对象的id，从而破坏拦截器接口的统一性
       - [improve] 对所有用户传入的sql语句，替换tab符号\t为空格
       - [improve] 修改拦截器beforeDelete和afterDelete的记录对象为List<T>
       - [improve] 优化deleteByKey(List<T> list)，当所有对象是同类且只有一个主键时，使用批量删除方式
       - [improve] 简化拦截器，去掉当有List或T对象时，Class的参数，该参数不再必要且有可能有歧义；去掉泛型
       - [improve] 当update是调用批量接口更新时，则批量只调用拦截器一次

2018年5月10日
v0.7.7 - [add] 增加getAllKey的接口

2018年5月7日
v0.7.6 - [fix] 修复当deleteByKey和拦截器并存时，试图在软删除前设置数据，无法设置的问题

2018年5月6日
v0.7.5 - [fix] 修复DBHelperInterceptor拦截器beforeUpdateCustom和beforeDeleteCustom参数问题
v0.7.4 - [add] DBHelperInterceptor拦截器beforeUpdateCustom和beforeDeleteCustom增加自定义set扩展
       - [improve] RelatedColumn关联查询参数查询时去重

2018年5月5日
v0.7.3 - [add] DBHelper增加setTimeoutWarningCallback方法
       - [improve] 将IDBHelperDataService从com.pugwoo.dbhelper.annotaion移到com.pugwoo.dbheper

2018年5月3日
v0.7.2 - [add] DBHelper增加delete(List)方法

2018年4月24日
v0.7.1 - [fix] 修复insertOrUpdateFull方法返回数据库修改条数没有算上删除条数的问题
v0.7.0 - [add] 增加Column注解对JSON的支持

2018年3月20日
v0.6.4 - [improve] 对于RowMapper中映射出错的，抛出RowMapperFailException

2018年3月14日
v0.6.3 - [improve] dbhelper两个getByKey接口参数类型由Class<?>改成Class<T>

2018年3月7日
v0.6.2 - [add] 增加dbHelper的executeAfterCommit方法，用于实现事务提交后执行指定代码
       - [improve] 新增修改删除的拦截器after*方法，如果当前有事务，则安排在事务提交后再执行
       
2018年3月1日
v0.6.1 - [fix] 修改JoinTable中计算列的字段映射问题
       - [add] 新增单独处理RelatedColumn的方法: handleRelatedColumn

2018年2月24日
v0.6.0 - [add] IDBHelperDataService接口方法带上
               Class<?> localClass, String localColumn,
			   Class<?> remoteClass, String remoteColumn四个参数，方便该接口支持更多功能
       - [del] 去掉DOInfoReader中对反射类信息的缓存
       - [add] 增加ExcludeInheritedColumn排除掉父类字段的读取的特性
       - [improve] RelatedColumn注解的字段，当查询不到数据且原字段值不为null时，不改动原字段的值

2018年1月24日
v0.5.2 - [improve] 允许拦截器在insert/update/delete对象前对对象进行修改

2017年12月22日
v0.5.1 - [fix] 当update对象时，如果软删除标记为是null，则自动设置为软删除的未删除状态
       - [improve] 查询相关代码优化

2017年10月9日
v0.5.0 - [add] 增加拦截器

2017年7月31日
v0.4.0 - [del] 删除掉DBHelper中对JDBCTemplate native封装的方法(queryForObject等4个)
       - [improve] 优化@RelatedColumn注解语义准确性，value修改为localColumn和remoteColumn必填
       - [test] 使用Tomcat JDBC代替druid

2017年6月10日
v0.3.7 - [add] 增加isExist和isExistAtLeast两个方法
       - [del] 标记DBHelper中jdbcTemplate的基本封装方法为废弃

2017年5月27日
v0.3.6 - [add] 增加单表select表别名t配置，join表别名支持配置
       - [add] 增加updateAll接口

2017年5月26日
v0.3.5 - [add] 增加自定义计算列

2017年5月17日
v0.3.4 - [add] 增加RelatedColumn的extraWhere属性

2017年5月12日
v0.3.3 - [fix] 修复查询最后一页时，计算总数出错的问题

2017年5月5日
v0.3.2 - [fix] 关联查询时，查询总数使用FOUND_ROWS()返回结果是错的。

2017年5月4日
v0.3.1 - [fix] 修复查分页条件有group by时,总页数计算问题; 该版本起查询总数均使用FOUND_ROWS()。
       - [add] 当查询参数传入为List,Set,[]时，如果其值为空，自动加入null值，这样in (?)查询条件不会报错。

2017年4月6日
v0.3.0 - [add] 增加join查询的接口

2017年3月18日
v0.2.0 - [add] 增加关联查询@RelatedColumn
         [del] 删除<T> T getByKey(Class<?> clazz, Map<String, Object> keyMap)接口，原因：容易使用出错

2017年3月17日
v0.1.7 - [fix] 修复软删除where的优先级问题,当包含or逻辑的情况

2017年3月13日
v0.1.6 - [fix] 修复数据库tinyint(1)到Java Integer类型的转换问题

2017年3月7日
v0.1.5 - [add] 增加insertWhereNotExist中where条件自动加上软删除标记
         [add] 增加批量insert方法
         [add] 增加随机主键配置
         
2016年11月25日
v0.1.3 - [add] @Column软删除标记,softDelete=[0,1]
         [add] 增加@Column默认值、插入和更新时设置Date
         [add] 增加全量增删list

2016年11月15日
v0.1.2 - [fix] db的tinyint(4)映射到java Boolean全是false的问题。

2016年10月13日
v0.1.1 - [add] update默认修改为只更新null值

2016年6月28日
v0.0.1 - [add] 支持in (?)输入参数为数组类型

